"""
Flask应用入口文件
"""

import os
import logging
from flask import Flask, jsonify
from flask_cors import CORS
from flask_socketio import Socket<PERSON>
from dotenv import load_dotenv, find_dotenv

from app.config.settings import settings
from app.api.translate import translate_bp
from app.api.websocket import init_websocket
from app.services.websocket_service import websocket_service

# 加载环境变量
load_dotenv(find_dotenv())


def create_app() -> tuple[Flask, SocketIO]:
    """创建Flask应用和SocketIO实例"""
    app = Flask(__name__)

    # 基础配置
    app.config["SECRET_KEY"] = settings.SECRET_KEY
    app.config["DEBUG"] = settings.DEBUG

    # 配置日志
    if not app.debug:
        logging.basicConfig(level=logging.INFO)

    # 配置CORS
    CORS(
        app,
        origins=settings.cors_origins_list,
        methods=["GET", "POST", "OPTIONS"],
        allow_headers=[
            "Content-Type",
            "Authorization",
            "X-Requested-With",
            "Accept",
            "Origin",
        ],
        supports_credentials=True,
    )

    # 创建SocketIO实例
    socketio = SocketIO(
        app,
        cors_allowed_origins=settings.cors_origins_list,
        async_mode="threading",  # 改为threading模式以支持多线程
        logger=app.debug,
        engineio_logger=app.debug,
    )

    # 初始化WebSocket服务
    websocket_service.set_socketio(socketio)
    init_websocket(app, socketio)

    # 注册蓝图
    app.register_blueprint(translate_bp, url_prefix="/api")

    # 全局OPTIONS请求处理器
    @app.before_request
    def handle_preflight():
        from flask import request, make_response

        if request.method == "OPTIONS":
            response = make_response()
            response.headers.add("Access-Control-Allow-Origin", "*")
            response.headers.add("Access-Control-Allow-Headers", "*")
            response.headers.add("Access-Control-Allow-Methods", "*")
            return response

    # 全局错误处理
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({"success": False, "error": "接口不存在"}), 404

    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({"success": False, "error": "服务器内部错误"}), 500

    return app, socketio


def main():
    """主函数"""
    app, socketio = create_app()

    print(f"Starting translation service on {settings.HOST}:{settings.PORT}")
    print(f"Debug mode: {settings.DEBUG}")
    print(f"CORS origins: {settings.cors_origins_list}")

    print(
        os.getenv("DASHSCOPE_API_KEY"),
        os.getenv("DASHSCOPE_MODEL", "qwen-turbo"),
        float(os.getenv("DASHSCOPE_TEMPERATURE", "0.7")),
    )

    # 启动应用（使用SocketIO）
    socketio.run(
        app,
        host=settings.HOST,
        port=settings.PORT,
        debug=settings.DEBUG,
        allow_unsafe_werkzeug=True,
    )


if __name__ == "__main__":
    main()
